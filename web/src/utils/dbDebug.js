/**
 * IndexedDB调试工具
 * 用于检查数据库状态和数据持久化问题
 */

import { 
  openDb, 
  getChatMessages, 
  getChatList, 
  addTabItem, 
  clearAllData,
  isDatabaseConnected,
  reconnectDatabase,
  getUnreadCount
} from './db.js'

/**
 * 检查数据库状态
 */
export const checkDatabaseStatus = async () => {
  console.log('=== IndexedDB状态检查 ===')
  
  try {
    // 检查连接状态
    const isConnected = isDatabaseConnected()
    console.log('数据库连接状态:', isConnected ? '已连接' : '未连接')
    
    // 尝试打开数据库
    await openDb()
    console.log('✅ 数据库打开成功')
    
    // 检查用户ID
    const userId = localStorage.getItem('userId') || sessionStorage.getItem('userId') || 'default'
    console.log('当前用户ID:', userId)
    
    // 获取聊天列表
    const chatList = await getChatList()
    console.log('聊天列表数量:', chatList.length)
    console.log('聊天列表详情:', chatList)
    
    // 获取未读消息数量
    const unreadCount = await getUnreadCount()
    console.log('总未读消息数:', unreadCount)
    
    return {
      isConnected: true,
      userId,
      chatCount: chatList.length,
      unreadCount,
      chatList
    }
  } catch (error) {
    console.error('❌ 数据库状态检查失败:', error)
    return {
      isConnected: false,
      error: error.message
    }
  }
}

/**
 * 添加测试数据
 */
export const addTestData = async () => {
  console.log('=== 添加测试数据 ===')
  
  const testMessages = [
    {
      id: "test_msg_001",
      typecode: 1,
      typecode2: 0,
      toid: 12345,
      fromid: 67890,
      chatid: 12345,
      t: new Date().toISOString(),
      msg: "这是一条测试消息",
      isRedRead: 0,
      idDel: 0,
      avatar: "https://example.com/avatar1.jpg",
      nickname: "测试用户",
      senderAvatar: "https://example.com/sender1.jpg",
      senderNickname: "测试用户",
      lastMessage: "这是一条测试消息",
      timestamp: new Date().getTime(),
      unreadCount: 1
    },
    {
      id: "test_msg_002",
      typecode: 2,
      typecode2: 0,
      toid: 54321,
      fromid: 67890,
      chatid: 54321,
      t: new Date().toISOString(),
      msg: "这是群聊测试消息",
      isRedRead: 0,
      idDel: 0,
      avatar: "https://example.com/avatar2.jpg",
      nickname: "群聊测试",
      senderAvatar: "https://example.com/sender2.jpg",
      senderNickname: "群聊测试",
      lastMessage: "这是群聊测试消息",
      timestamp: new Date().getTime(),
      unreadCount: 1
    }
  ]
  
  try {
    for (const msg of testMessages) {
      const result = await addTabItem(msg)
      console.log('✅ 测试消息添加成功:', result.id)
    }
    
    console.log('所有测试数据添加完成')
    return true
  } catch (error) {
    console.error('❌ 添加测试数据失败:', error)
    return false
  }
}

/**
 * 检查特定聊天的消息
 */
export const checkChatMessages = async (chatId) => {
  console.log(`=== 检查聊天消息 (chatId: ${chatId}) ===`)
  
  try {
    const messages = await getChatMessages(chatId, 1, 50)
    console.log('消息数量:', messages.length)
    console.log('消息详情:', messages)
    
    return messages
  } catch (error) {
    console.error('❌ 检查聊天消息失败:', error)
    return []
  }
}

/**
 * 清空所有数据（谨慎使用）
 */
export const clearAllTestData = async () => {
  console.log('=== 清空所有数据 ===')
  
  try {
    await clearAllData()
    console.log('✅ 所有数据已清空')
    return true
  } catch (error) {
    console.error('❌ 清空数据失败:', error)
    return false
  }
}

/**
 * 完整的数据库测试
 */
export const runFullDatabaseTest = async () => {
  console.log('=== 开始完整数据库测试 ===')
  
  try {
    // 1. 检查状态
    const status = await checkDatabaseStatus()
    console.log('状态检查结果:', status)
    
    // 2. 清空现有数据
    await clearAllTestData()
    
    // 3. 添加测试数据
    await addTestData()
    
    // 4. 重新检查状态
    const newStatus = await checkDatabaseStatus()
    console.log('添加数据后状态:', newStatus)
    
    // 5. 检查特定聊天消息
    const messages1 = await checkChatMessages(12345)
    const messages2 = await checkChatMessages(54321)
    
    console.log('=== 测试完成 ===')
    return {
      success: true,
      status: newStatus,
      messages: {
        chat12345: messages1,
        chat54321: messages2
      }
    }
  } catch (error) {
    console.error('❌ 完整测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 页面刷新后的数据恢复测试
 */
export const testDataPersistence = async () => {
  console.log('=== 测试数据持久化 ===')
  
  try {
    // 模拟页面刷新后的数据加载
    console.log('模拟页面刷新...')
    
    // 重新连接数据库
    await reconnectDatabase()
    
    // 检查数据是否还在
    const status = await checkDatabaseStatus()
    
    if (status.chatCount > 0) {
      console.log('✅ 数据持久化正常，找到', status.chatCount, '个聊天记录')
      return true
    } else {
      console.log('❌ 数据持久化失败，没有找到聊天记录')
      return false
    }
  } catch (error) {
    console.error('❌ 数据持久化测试失败:', error)
    return false
  }
}

// 将调试函数挂载到window对象，方便在浏览器控制台中使用
if (typeof window !== 'undefined') {
  window.dbDebug = {
    checkStatus: checkDatabaseStatus,
    addTestData,
    checkMessages: checkChatMessages,
    clearData: clearAllTestData,
    runFullTest: runFullDatabaseTest,
    testPersistence: testDataPersistence,
    debugAll: async () => {
      const { debugAllMessages } = await import('./db.js')
      return await debugAllMessages()
    },
    testQuery: async (chatId) => {
      console.log('=== 测试chatId查询 ===')
      console.log('输入chatId:', chatId, '类型:', typeof chatId)

      const { getChatMessages } = await import('./db.js')
      const result = await getChatMessages(chatId)

      console.log('查询结果:', result)
      return result
    }
  }
  
  console.log('🔧 数据库调试工具已加载，使用 window.dbDebug 访问')
  console.log('可用方法:')
  console.log('- window.dbDebug.checkStatus() - 检查数据库状态')
  console.log('- window.dbDebug.addTestData() - 添加测试数据')
  console.log('- window.dbDebug.checkMessages(chatId) - 检查特定聊天消息')
  console.log('- window.dbDebug.clearData() - 清空所有数据')
  console.log('- window.dbDebug.runFullTest() - 运行完整测试')
  console.log('- window.dbDebug.testPersistence() - 测试数据持久化')
}
