
// 数据库配置
export const DB_NAME = 'chatDb'
export const DB_VERSION = 2  // 升级版本以支持新的数据结构

// 获取当前用户ID
const getCurrentUserId = () => {
  return localStorage.getItem('userId') || sessionStorage.getItem('userId') || 'default'
}

// 获取表名
export const getTableName = () => `chatData_${getCurrentUserId()}`

// 数据库实例
let db = null

// 存储当前数据库版本，用于动态升级
let currentDbVersion = DB_VERSION

/**
 * 数据结构定义
 * {
 *   id: String,              // 消息ID
 *   typecode: Number,        // 消息类型
 *   typecode2: Number,       // 内容类型
 *   toid: Number,           // 接收者ID
 *   fromid: Number,         // 发送者ID
 *   chatid: Number,         // 聊天对象ID
 *   t: String,              // 时间戳
 *   msg: String,            // 解密后消息内容
 *   isRedRead: Number,      // 已读状态 (0-未读，1-已读)
 *   idDel: Number,          // 删除状态 (0-正常，1-已删除)
 *   avatar: String,         // 发送者头像URL
 *   nickname: String,       // 发送者昵称
 *   senderAvatar: String,   // 群聊中发送者头像
 *   senderNickname: String, // 群聊中发送者昵称
 *   lastMessage: String,    // 聊天列表显示文本
 *   timestamp: Number,      // 时间戳(数字格式)
 *   unreadCount: Number     // 未读消息数
 * }
 */

/**
 * 确保对象存储存在
 * @returns {Promise<IDBDatabase>}
 */
const ensureObjectStore = () => {
  return new Promise((resolve, reject) => {
    const tableName = getTableName()

    // 如果数据库不存在或对象存储不存在，需要升级
    if (!db || !db.objectStoreNames.contains(tableName)) {
      if (db) {
        db.close()
        db = null
      }

      // 递增版本号
      currentDbVersion++
      const request = indexedDB.open(DB_NAME, currentDbVersion)

      request.onerror = () => {
        console.error('数据库升级失败:', request.error)
        reject(request.error)
      }

      request.onsuccess = () => {
        db = request.result
        console.log('数据库升级成功，版本:', currentDbVersion)
        resolve(db)
      }

      request.onupgradeneeded = (event) => {
        db = event.target.result
        console.log('数据库升级中，创建对象存储:', tableName)

        if (!db.objectStoreNames.contains(tableName)) {
          const store = db.createObjectStore(tableName, { keyPath: 'id' })

          // 创建完整的索引以匹配数据结构
          store.createIndex('chatid', 'chatid', { unique: false })
          store.createIndex('fromid', 'fromid', { unique: false })
          store.createIndex('toid', 'toid', { unique: false })
          store.createIndex('t', 't', { unique: false })
          store.createIndex('timestamp', 'timestamp', { unique: false })
          store.createIndex('typecode', 'typecode', { unique: false })
          store.createIndex('typecode2', 'typecode2', { unique: false })
          store.createIndex('isRedRead', 'isRedRead', { unique: false })
          store.createIndex('idDel', 'idDel', { unique: false })
          store.createIndex('nickname', 'nickname', { unique: false })
          store.createIndex('senderNickname', 'senderNickname', { unique: false })

          console.log('消息表创建成功:', tableName)
        }
      }
    } else {
      resolve(db)
    }
  })
}

/**
 * 打开数据库
 * @returns {Promise<IDBDatabase>}
 */
export const openDb = () => {
  return new Promise((resolve, reject) => {
    if (db) {
      resolve(db)
      return
    }

    const request = indexedDB.open(DB_NAME, currentDbVersion)

    request.onerror = () => {
      console.error('数据库打开失败:', request.error)
      reject(request.error)
    }

    request.onsuccess = () => {
      db = request.result
      console.log('数据库打开成功')
      resolve(db)
    }

    request.onupgradeneeded = (event) => {
      db = event.target.result
      console.log('数据库升级中...')

      const tableName = getTableName()
      if (!db.objectStoreNames.contains(tableName)) {
        const store = db.createObjectStore(tableName, { keyPath: 'id' })

        // 创建完整的索引以匹配数据结构
        store.createIndex('chatid', 'chatid', { unique: false })
        store.createIndex('fromid', 'fromid', { unique: false })
        store.createIndex('toid', 'toid', { unique: false })
        store.createIndex('t', 't', { unique: false })
        store.createIndex('timestamp', 'timestamp', { unique: false })
        store.createIndex('typecode', 'typecode', { unique: false })
        store.createIndex('typecode2', 'typecode2', { unique: false })
        store.createIndex('isRedRead', 'isRedRead', { unique: false })
        store.createIndex('idDel', 'idDel', { unique: false })
        store.createIndex('nickname', 'nickname', { unique: false })
        store.createIndex('senderNickname', 'senderNickname', { unique: false })

        console.log('消息表创建成功:', tableName)
      }
    }
  })
}

/**
 * 关闭数据库
 */
export const closeDb = () => {
  if (db) {
    db.close()
    db = null
    console.log('数据库已关闭')
  }
}

/**
 * 创建表
 * @returns {Promise<boolean>}
 */
export const addTab = async () => {
  try {
    await openDb()
    return true
  } catch (error) {
    console.error('创建表失败:', error)
    return false
  }
}

/**
 * 添加消息到数据库
 * @param {Object} item - 消息对象
 * @returns {Promise<Object>}
 */
export const addTabItem = async (item) => {
  try {
    if (!item) {
      throw new Error('参数不能为空')
    }

    // 确保对象存储存在
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())

      // 处理数据字段，完全匹配你的数据结构
      const processedItem = {
        id: String(item.id) || String(Date.now()),           // 消息ID (String)
        typecode: Number(item.typecode) || 0,                // 消息类型 (Number)
        typecode2: Number(item.typecode2) || 0,              // 内容类型 (Number)
        toid: Number(item.toid) || 0,                        // 接收者ID (Number)
        fromid: Number(item.fromid) || 0,                    // 发送者ID (Number)
        chatid: Number(item.chatid) || 0,                    // 聊天对象ID (Number)
        t: String(item.t || new Date().toISOString()),       // 时间戳 (String)
        msg: String(item.msg || ''),                         // 解密后消息内容 (String)
        isRedRead: Number(item.isRedRead) || 0,              // 已读状态 (Number: 0-未读，1-已读)
        idDel: Number(item.idDel) || 0,                      // 删除状态 (Number: 0-正常，1-已删除)
        avatar: String(item.avatar || ''),                   // 发送者头像URL (String)
        nickname: String(item.nickname || ''),               // 发送者昵称 (String)
        senderAvatar: String(item.senderAvatar || ''),       // 群聊中发送者头像 (String)
        senderNickname: String(item.senderNickname || ''),   // 群聊中发送者昵称 (String)
        lastMessage: String(item.lastMessage || item.msg || ''), // 聊天列表显示文本 (String)
        timestamp: Number(item.timestamp) || new Date().getTime(), // 时间戳(数字格式) (Number)
        unreadCount: Number(item.unreadCount) || 0           // 未读消息数 (Number)
      }

      // 验证必要字段
      if (!processedItem.toid || !processedItem.fromid || !processedItem.chatid) {
        reject(new Error('必要字段缺失：toid, fromid, chatid'))
        return
      }

      // 如果没有提供lastMessage，使用msg作为默认值
      if (!processedItem.lastMessage && processedItem.msg) {
        processedItem.lastMessage = processedItem.msg
      }

      const request = store.put(processedItem)

      request.onsuccess = () => {
        console.log('消息添加成功:', processedItem.id)
        resolve(processedItem)
      }

      request.onerror = () => {
        console.error('消息添加失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('添加消息处理错误:', error)
    throw error
  }
}

/**
 * 根据chatId获取聊天消息
 * @param {string} chatId - 聊天对象ID
 * @param {number} page - 页码
 * @param {number} size - 每页数量
 * @returns {Promise<Array>}
 */
export const getChatMessages = async (chatId, page = 1, size = 20) => {
  try {
    if (!chatId) {
      throw new Error('chatId is required')
    }

    // 确保对象存储存在
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const index = store.index('chatid')

      const request = index.getAll(chatId.toString())

      request.onsuccess = () => {
        let messages = request.result || []

        // 按时间戳正序排序（优先使用timestamp，fallback到t字段）
        messages.sort((a, b) => {
          const timeA = a.timestamp || new Date(a.t).getTime()
          const timeB = b.timestamp || new Date(b.t).getTime()
          return timeA - timeB
        })

        // 分页处理 - 从最新的消息开始分页
        const totalMessages = messages.length
        const startIndex = Math.max(0, totalMessages - page * size)
        const endIndex = totalMessages - (page - 1) * size
        const paginatedMessages = messages.slice(startIndex, endIndex)

        console.log(`获取聊天消息: chatId=${chatId}, 总数=${messages.length}, 返回=${paginatedMessages.length}`)
        resolve(paginatedMessages)
      }

      request.onerror = () => {
        console.error('获取聊天消息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取聊天消息处理错误:', error)
    throw error
  }
}

/**
 * 获取聊天对象列表
 * @returns {Promise<Array>}
 */
export const getChatList = async () => {
  try {
    // 确保对象存储存在
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const request = store.getAll()

      request.onsuccess = () => {
        const messages = request.result || []

        // 按chatid分组，获取每个聊天对象的最新消息
        const chatMap = new Map()

        messages.forEach(message => {
          const chatId = message.chatid
          const currentTime = message.timestamp || new Date(message.t).getTime()
          const existingMessage = chatMap.get(chatId)
          const existingTime = existingMessage ? (existingMessage.timestamp || new Date(existingMessage.t).getTime()) : 0

          if (!chatMap.has(chatId) || currentTime > existingTime) {
            chatMap.set(chatId, message)
          }
        })

        // 转换为数组并按时间排序
        const chatList = Array.from(chatMap.values())
          .sort((a, b) => new Date(b.t).getTime() - new Date(a.t).getTime())
          .map(message => ({
            chatId: message.chatid,
            name: message.nickname || message.senderNickname || `用户${message.chatid}`,
            avatar: message.avatar || message.senderAvatar || '/static/My/avatar.jpg',
            lastMessage: message.lastMessage || message.msg || '',
            lastTime: message.t,
            timestamp: message.timestamp,
            unreadCount: message.unreadCount || 0,
            type: message.typecode === 2 ? 'group' : 'contact'
          }))

        console.log('获取聊天列表成功:', chatList.length, '个')
        resolve(chatList)
      }

      request.onerror = () => {
        console.error('获取聊天列表失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取聊天列表处理错误:', error)
    throw error
  }
}

/**
 * 标记消息为已读
 * @param {string} chatId - 聊天对象ID
 * @returns {Promise<boolean>}
 */
export const markMessagesAsRead = async (chatId) => {
  try {
    if (!chatId) {
      throw new Error('chatId is required')
    }

    // 确保对象存储存在
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())
      const index = store.index('chatid')

      const request = index.getAll(chatId.toString())

      request.onsuccess = () => {
        const messages = request.result || []
        const unreadMessages = messages.filter(m => m.isRedRead === 0)

        if (unreadMessages.length === 0) {
          resolve(true)
          return
        }

        let updateCount = 0

        unreadMessages.forEach(message => {
          message.isRedRead = 1

          const updateRequest = store.put(message)

          updateRequest.onsuccess = () => {
            updateCount++
            if (updateCount === unreadMessages.length) {
              console.log(`标记消息为已读: chatId=${chatId}, 更新数量=${updateCount}`)
              resolve(true)
            }
          }

          updateRequest.onerror = () => {
            console.error('更新消息已读状态失败:', updateRequest.error)
            reject(updateRequest.error)
          }
        })
      }

      request.onerror = () => {
        console.error('获取消息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('标记消息为已读处理错误:', error)
    throw error
  }
}

/**
 * 清空所有数据
 * @returns {Promise<boolean>}
 */
export const clearAllData = async () => {
  try {
    // 确保对象存储存在
    await ensureObjectStore()
    const tableName = getTableName()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite')
      const store = transaction.objectStore(tableName)

      const clearRequest = store.clear()

      clearRequest.onsuccess = () => {
        console.log('数据库已清空')
        resolve(true)
      }

      clearRequest.onerror = () => {
        console.error('清空数据库失败:', clearRequest.error)
        reject(clearRequest.error)
      }
    })
  } catch (error) {
    console.error('清空数据库异常:', error)
    throw error
  }
}

/**
 * 根据消息ID获取消息
 * @param {string} messageId - 消息ID
 * @returns {Promise<Object|null>}
 */
export const getMessageById = async (messageId) => {
  try {
    if (!messageId) {
      throw new Error('messageId is required')
    }

    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const request = store.get(String(messageId))

      request.onsuccess = () => {
        const message = request.result
        console.log('获取消息成功:', messageId, message ? '找到' : '未找到')
        resolve(message || null)
      }

      request.onerror = () => {
        console.error('获取消息失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取消息处理错误:', error)
    throw error
  }
}

/**
 * 更新消息
 * @param {string} messageId - 消息ID
 * @param {Object} updates - 要更新的字段
 * @returns {Promise<Object>}
 */
export const updateMessage = async (messageId, updates) => {
  try {
    if (!messageId || !updates) {
      throw new Error('messageId and updates are required')
    }

    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readwrite')
      const store = transaction.objectStore(getTableName())

      // 先获取现有消息
      const getRequest = store.get(String(messageId))

      getRequest.onsuccess = () => {
        const existingMessage = getRequest.result
        if (!existingMessage) {
          reject(new Error('消息不存在'))
          return
        }

        // 合并更新
        const updatedMessage = { ...existingMessage, ...updates }

        // 确保ID不被修改
        updatedMessage.id = String(messageId)

        const putRequest = store.put(updatedMessage)

        putRequest.onsuccess = () => {
          console.log('消息更新成功:', messageId)
          resolve(updatedMessage)
        }

        putRequest.onerror = () => {
          console.error('消息更新失败:', putRequest.error)
          reject(putRequest.error)
        }
      }

      getRequest.onerror = () => {
        console.error('获取消息失败:', getRequest.error)
        reject(getRequest.error)
      }
    })
  } catch (error) {
    console.error('更新消息处理错误:', error)
    throw error
  }
}

/**
 * 删除消息（软删除，设置idDel=1）
 * @param {string} messageId - 消息ID
 * @returns {Promise<boolean>}
 */
export const deleteMessage = async (messageId) => {
  try {
    await updateMessage(messageId, { idDel: 1 })
    console.log('消息删除成功:', messageId)
    return true
  } catch (error) {
    console.error('删除消息失败:', error)
    throw error
  }
}

/**
 * 撤回消息（设置typecode2=5）
 * @param {string} messageId - 消息ID
 * @param {Object} retractInfo - 撤回信息 {ret: 撤回的消息id, other: 自定义}
 * @returns {Promise<boolean>}
 */
export const retractMessage = async (messageId, retractInfo = {}) => {
  try {
    const retractData = {
      typecode2: 5,
      msg: JSON.stringify({ ret: messageId, ...retractInfo })
    }
    await updateMessage(messageId, retractData)
    console.log('消息撤回成功:', messageId)
    return true
  } catch (error) {
    console.error('撤回消息失败:', error)
    throw error
  }
}

/**
 * 获取未读消息数量
 * @param {string} chatId - 聊天对象ID（可选，不传则获取所有未读数量）
 * @returns {Promise<number>}
 */
export const getUnreadCount = async (chatId = null) => {
  try {
    await ensureObjectStore()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([getTableName()], 'readonly')
      const store = transaction.objectStore(getTableName())
      const index = store.index('isRedRead')

      const request = index.getAll(0) // 获取所有未读消息

      request.onsuccess = () => {
        let messages = request.result || []

        // 如果指定了chatId，过滤该聊天的消息
        if (chatId) {
          messages = messages.filter(msg => msg.chatid === Number(chatId))
        }

        const unreadCount = messages.length
        console.log(`未读消息数量: ${chatId ? `chatId=${chatId}` : '全部'} = ${unreadCount}`)
        resolve(unreadCount)
      }

      request.onerror = () => {
        console.error('获取未读消息数量失败:', request.error)
        reject(request.error)
      }
    })
  } catch (error) {
    console.error('获取未读消息数量处理错误:', error)
    throw error
  }
}

/**
 * 初始化数据库（应用启动时调用）
 * @returns {Promise<boolean>}
 */
export const initDatabase = async () => {
  try {
    console.log('开始初始化IndexedDB数据库...')

    // 设置用户ID（如果localStorage中有的话）
    const userId = localStorage.getItem('userId') || sessionStorage.getItem('userId')
    if (userId) {
      console.log('找到用户ID:', userId)
    } else {
      console.log('未找到用户ID，使用默认值')
    }

    // 打开数据库
    await openDb()
    console.log('数据库初始化成功')

    // 检查数据库中是否有数据
    const chatList = await getChatList()
    console.log(`数据库中现有聊天记录数量: ${chatList.length}`)

    return true
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return false
  }
}

/**
 * 检查数据库连接状态
 * @returns {boolean}
 */
export const isDatabaseConnected = () => {
  return db !== null && db.readyState !== 'closed'
}

/**
 * 重新连接数据库
 * @returns {Promise<boolean>}
 */
export const reconnectDatabase = async () => {
  try {
    console.log('重新连接数据库...')
    if (db) {
      db.close()
      db = null
    }
    await openDb()
    console.log('数据库重连成功')
    return true
  } catch (error) {
    console.error('数据库重连失败:', error)
    return false
  }
}
