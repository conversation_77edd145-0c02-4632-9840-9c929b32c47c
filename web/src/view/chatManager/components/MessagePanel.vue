<template>
  <div class="message-panel">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-info">
        <el-avatar :size="30" :src="conversation.avatar">
          <el-icon v-if="conversation.type === 'group'"><ChatLineRound /></el-icon>
          <el-icon v-else><User /></el-icon>
        </el-avatar>
        <div class="info-text">
          <div class="name">{{ conversation.name }}</div>
          <!-- <div class="status" v-if="conversation.type === 'friend'">
            <span :class="conversation.online ? 'online' : 'offline'">
              {{ conversation.online ? '在线' : '离线' }}
            </span>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <el-scrollbar ref="scrollbarRef" class="message-scrollbar" @scroll="handleScroll">
        <div class="messages-container">
          <!-- 加载更多指示器 -->
          <div v-if="isLoadingMore" class="loading-more">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载更多消息...</span>
          </div>

          <!-- 没有更多消息提示 -->
          <div v-else-if="!hasMoreMessages && messages.length > 0" class="no-more-messages">
            没有更多消息了
          </div>

          <div class="message-content">
            <div v-for="(messageGroup, date) in groupedMessages" :key="date" class="message-group">
              <div class="date-divider">{{ date }}</div>
              <div v-for="message in messageGroup" :key="message.id" class="message-item">
                <MessageItem
                  :message="message"
                  :is-own="message.isOwn"
                  @avatar-click="handleAvatarClick"
                />
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 消息输入框 -->
    <div class="message-input">
      <MessageEditor @send="handleSendMessage" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import MessageItem from './MessageItem.vue'
import MessageEditor from './MessageEditor.vue'
import { sendMessage } from '@/utils/chatService.js'
import { addTabItem } from '@/utils/db.js'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'

defineOptions({
  name: 'MessagePanel'
})

const props = defineProps({
  conversation: {
    type: Object,
    required: true
  }
})

const messageListRef = ref()
const scrollbarRef = ref()

// 当前用户ID（聊天系统固定使用10003）
const currentUserId = ref('10003')

// 消息数据
const messages = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const hasMoreMessages = ref(true)
const isLoadingMore = ref(false)

// 加载聊天消息（初始加载）
const loadMessages = async (reset = true) => {
  try {
    if (!props.conversation) {
      console.log('没有选中的会话，跳过加载消息')
      return
    }

    const chatId = props.conversation.type === 'group'
      ? props.conversation.originalData?.ID?.toString()
      : props.conversation.id.replace('friend_', '')

    if (!chatId) {
      console.log('无法获取chatId，跳过加载消息')
      return
    }

    console.log('开始加载聊天消息，chatId:', chatId, 'chatId类型:', typeof chatId, '会话信息:', props.conversation)
    console.log('会话详细信息:', {
      type: props.conversation.type,
      id: props.conversation.id,
      originalData: props.conversation.originalData
    })

    if (reset) {
      // 重置分页状态
      currentPage.value = 1
      hasMoreMessages.value = true
      messages.value = []
    }

    // 确保数据库已初始化
    const { openDb, getChatMessages, debugAllMessages } = await import('@/utils/db.js')
    await openDb()

    // 调试：查看数据库中的所有数据
    if (currentPage.value === 1) {
      console.log('=== 调试数据库内容 ===')
      await debugAllMessages()
    }

    // 从数据库加载消息
    const dbMessages = await getChatMessages(chatId, currentPage.value, pageSize.value)
    console.log('从数据库加载的消息:', dbMessages)

    if (!dbMessages || dbMessages.length === 0) {
      console.log('数据库中没有找到消息')
      if (reset) {
        messages.value = []
      }
      hasMoreMessages.value = false
      return
    }

    // 转换消息格式
    const convertedMessages = dbMessages.map(msg => {
      const fromidStr = msg.fromid?.toString()
      const currentUserIdStr = currentUserId.value.toString()
      const isOwn = fromidStr === currentUserIdStr

      return {
        id: msg.id,
        userId: fromidStr,
        nickname: isOwn ? '我' : (msg.senderNickname || msg.nickname || `用户${msg.fromid}`),
        avatar: msg.senderAvatar || msg.avatar || '',
        content: msg.msg,
        type: getMessageType(msg.typecode2),
        createdAt: msg.timestamp ? new Date(msg.timestamp) : new Date(msg.t),
        isOwn: isOwn,
        status: msg.isRedRead ? 'read' : 'sent'
      }
    })

    if (reset) {
      messages.value = convertedMessages
    } else {
      // 分页加载时，将历史消息添加到顶部
      messages.value = [...convertedMessages, ...messages.value]
    }

    // 检查是否还有更多消息
    if (dbMessages.length < pageSize.value) {
      hasMoreMessages.value = false
    }

    console.log('转换后的消息数量:', messages.value.length)

    // 延迟滚动到底部（仅初始加载时）
    if (reset) {
      nextTick(() => {
        scrollToBottom()
      })
    }

  } catch (error) {
    console.error('加载聊天消息失败:', error)
    if (reset) {
      messages.value = []
    }
  }
}

// 加载更多消息（分页）
const loadMoreMessages = async () => {
  if (isLoadingMore.value || !hasMoreMessages.value) {
    return
  }

  try {
    isLoadingMore.value = true
    currentPage.value++

    // 记录当前滚动位置
    const messagesContainer = document.querySelector('.messages-container')
    const scrollHeight = messagesContainer?.scrollHeight || 0

    await loadMessages(false)

    // 恢复滚动位置（保持在加载前的位置）
    if (messagesContainer) {
      nextTick(() => {
        const newScrollHeight = messagesContainer.scrollHeight
        const scrollDiff = newScrollHeight - scrollHeight
        messagesContainer.scrollTop = scrollDiff
      })
    }

  } catch (error) {
    console.error('加载更多消息失败:', error)
    currentPage.value-- // 回退页码
  } finally {
    isLoadingMore.value = false
  }
}

// 根据typecode2获取消息类型
const getMessageType = (typecode2) => {
  switch (typecode2) {
    case 0: return 'text'
    case 1: return 'audio'
    case 2: return 'image'
    case 3: return 'video'
    case 4: return 'forward'
    case 5: return 'retract'
    default: return 'text'
  }
}

// 按日期分组消息
const groupedMessages = computed(() => {
  const groups = {}
  messages.value.forEach(message => {
    const date = formatDate(message.createdAt)
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(message)
  })
  return groups
})

// 格式化日期
const formatDate = (date) => {
  const now = new Date()
  const messageDate = new Date(date)
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const messageDay = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate())

  if (messageDay.getTime() === today.getTime()) {
    return '今天'
  } else if (messageDay.getTime() === yesterday.getTime()) {
    return '昨天'
  } else {
    return messageDate.toLocaleDateString('zh-CN', { 
      month: 'long', 
      day: 'numeric' 
    })
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (scrollbarRef.value) {
      const scrollbar = scrollbarRef.value
      const wrapRef = scrollbar.wrapRef
      wrapRef.scrollTop = wrapRef.scrollHeight
    }
  })
}

// 处理滚动事件
const handleScroll = (scrollInfo) => {
  // 当滚动到顶部附近时，加载更多消息
  if (scrollInfo.scrollTop < 100 && hasMoreMessages.value && !isLoadingMore.value) {
    loadMoreMessages()
  }
}

// 发送消息
const handleSendMessage = async (content) => {
  try {
    if (!props.conversation || !content.trim()) return

    const isGroup = props.conversation.type === 'group'
    const targetId = isGroup
      ? props.conversation.originalData?.ID
      : parseInt(props.conversation.id.replace('friend_', ''))

    if (!targetId) {
      ElMessage.error('无法获取目标ID')
      return
    }

    // 构建消息参数
    const messageParams = {
      fromid: parseInt(currentUserId.value),
      toId: targetId,
      msg: content.trim(),
      typecode: isGroup ? 2 : 1, // 1-好友消息，2-群组消息
      typecode2: 0, // 0-文本消息
      ...(isGroup && { groupID: targetId })
    }

    console.log('发送消息参数:', messageParams)

    // 发送消息到服务器
    const result = await sendMessage(messageParams)
    console.log('消息发送结果:', result)

    // 立即添加到本地消息列表（乐观更新）
    const newMessage = {
      id: Date.now(),
      userId: currentUserId.value,
      nickname: '我',
      avatar: '',
      content: content.trim(),
      type: 'text',
      createdAt: new Date(),
      isOwn: true
    }

    messages.value.push(newMessage)
    scrollToBottom()

    // 保存到本地数据库
    const messageItem = {
      id: Date.now(),
      typecode: messageParams.typecode,
      typecode2: messageParams.typecode2,
      toid: messageParams.toId,
      fromid: messageParams.fromid,
      chatid: targetId.toString(),
      t: new Date().toISOString(),
      msg: content.trim(),
      isRedRead: 1, // 自己发送的消息标记为已读
      idDel: 0,
      senderAvatar: '',
      senderNickname: '我',
      avatar: '',
      nickname: isGroup ? props.conversation.name : '我',
      lastMessage: content.trim(),
      timestamp: new Date().getTime(),
      unreadCount: 0
    }

    await addTabItem(messageItem)
    console.log('消息已保存到本地数据库')

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  }
}

// 头像点击
const handleAvatarClick = (message) => {
  console.log('点击头像:', message)
}

// WebSocket消息监听器
let messageListener = null

// 设置消息监听
const setupMessageListener = () => {
  messageListener = (data) => {
    console.log('收到WebSocket消息:', data)

    // 检查是否是当前会话的消息
    if (!props.conversation) return

    const isGroup = props.conversation.type === 'group'
    const currentChatId = isGroup
      ? props.conversation.originalData?.ID?.toString()
      : props.conversation.id.replace('friend_', '')

    const messageChatId = isGroup
      ? data.groupID?.toString() || data.toid?.toString()
      : data.fromid?.toString()

    if (currentChatId === messageChatId) {
      // 添加接收到的消息到列表
      const fromidStr = data.fromid?.toString()
      const currentUserIdStr = currentUserId.value.toString()
      const isOwn = fromidStr === currentUserIdStr

      console.log(`接收消息详情:`, {
        id: data.id,
        fromid: data.fromid,
        fromidType: typeof data.fromid,
        fromidStr: fromidStr,
        currentUserId: currentUserId.value,
        currentUserIdType: typeof currentUserId.value,
        currentUserIdStr: currentUserIdStr,
        isOwn: isOwn,
        msg: data.msg
      })

      const receivedMessage = {
        id: data.id || Date.now(),
        userId: fromidStr,
        nickname: isOwn ? '我' : (data.senderNickname || `用户${data.fromid}`),
        avatar: data.senderAvatar || '',
        content: data.msg,
        type: 'text',
        createdAt: new Date(data.t || new Date()),
        isOwn: isOwn
      }

      // 避免重复添加自己发送的消息
      if (!receivedMessage.isOwn) {
        messages.value.push(receivedMessage)
        scrollToBottom()
      }
    }
  }

  // 直接从WebSocket管理器添加消息处理器
  import('@/utils/websocket.js').then(({ default: webSocketManager }) => {
    webSocketManager.addMessageHandler('message', messageListener)
  })
}

// 移除消息监听
const removeMessageListener = () => {
  if (messageListener) {
    import('@/utils/websocket.js').then(({ default: webSocketManager }) => {
      webSocketManager.removeMessageHandler('message')
      messageListener = null
    })
  }
}

// 组件挂载时设置监听器
onMounted(() => {
  console.log('MessagePanel组件挂载，当前会话:', props.conversation)
  setupMessageListener()
  if (props.conversation) {
    loadMessages(true)
  }
})

// 组件卸载时移除监听器
onUnmounted(() => {
  removeMessageListener()
})

// 监听会话变化，重新加载消息
watch(() => props.conversation, (newConversation) => {
  if (newConversation) {
    console.log('会话切换，重新加载消息:', newConversation)
    // 重置状态并加载消息
    loadMessages(true)
  }
}, { immediate: true })

// 监听消息变化，滚动到底部（仅在非分页加载时）
watch(messages, (newMessages, oldMessages) => {
  // 如果是新增消息（不是分页加载），则滚动到底部
  if (newMessages.length > oldMessages.length && !isLoadingMore.value) {
    scrollToBottom()
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.message-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #374151;
}

.chat-header {
  padding: 13px 20px;
  background: #4b5563;
  border-bottom: 1px solid #6b7280;

  .header-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .info-text {
      .name {
        font-size: 16px;
        font-weight: 500;
        color: #f3f4f6;
        margin-bottom: 2px;
      }

      .status {
        font-size: 12px;

        .online {
          color: #22c55e;
        }

        .offline {
          color: #9ca3af;
        }
      }
    }
  }
}

.message-list {
  flex: 1;
  overflow: hidden;
  // height: calc(750px - 120px - 80px); // 总高度 - 头部 - 输入框
  // min-height: calc(750px - 120px - 80px);
  // max-height: calc(750px - 120px - 80px);

  .message-scrollbar {
    height: 100%;
  }

  .message-content {
    padding: 20px;
  }

  .message-group {
    margin-bottom: 20px;

    .date-divider {
      text-align: center;
      color: #9ca3af;
      font-size: 12px;
      margin-bottom: 15px;
      position: relative;

      /* &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 30%;
        height: 1px;
        background: #6b7280;
      }
 */
      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }

    .message-item {
      margin-bottom: 15px;
    }
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    color: #9ca3af;
    font-size: 14px;
    gap: 8px;

    .el-icon {
      font-size: 16px;
    }
  }

  .no-more-messages {
    text-align: center;
    padding: 15px;
    color: #6b7280;
    font-size: 12px;
  }
}

.message-input {
  background: #4b5563;
  border-top: 1px solid #6b7280;
}
</style>
