// 测试数据库功能
import { openDb, addTabItem, getChatMessages, getTableName } from './src/utils/db.js'

async function testDatabase() {
  console.log('开始测试数据库功能...')
  
  try {
    // 模拟用户登录
    localStorage.setItem('userId', 'test_user_123')
    
    console.log('当前表名:', getTableName())
    
    // 测试打开数据库
    console.log('1. 测试打开数据库...')
    const database = await openDb()
    console.log('数据库打开成功:', database.name, '版本:', database.version)
    
    // 测试添加消息
    console.log('2. 测试添加消息...')
    const testMessage = {
      id: Date.now(),
      typecode: 1,
      typecode2: 0,
      toid: 'user_456',
      fromid: 'test_user_123',
      chatid: 'chat_789',
      t: new Date().toISOString(),
      isRedRead: 0,
      idDel: 0,
      msg: '这是一条测试消息',
      senderAvatar: '/static/My/avatar.jpg',
      senderNickname: '测试用户',
      avatar: '/static/My/avatar.jpg',
      nickname: '测试用户',
      lastMessage: '这是一条测试消息',
      timestamp: Date.now(),
      unreadCount: 1
    }
    
    const addedMessage = await addTabItem(testMessage)
    console.log('消息添加成功:', addedMessage.id)
    
    // 测试获取消息
    console.log('3. 测试获取消息...')
    const messages = await getChatMessages('chat_789')
    console.log('获取到消息数量:', messages.length)
    console.log('消息内容:', messages[0])
    
    console.log('数据库测试完成！')
    
  } catch (error) {
    console.error('数据库测试失败:', error)
  }
}

// 在浏览器环境中运行测试
if (typeof window !== 'undefined') {
  window.testDatabase = testDatabase
  console.log('数据库测试函数已加载，请在控制台运行 testDatabase() 进行测试')
}

export { testDatabase }
